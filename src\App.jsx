import { useState } from 'react'
import Header from './components/Header'
import ProjectGrid from './components/ProjectGrid'
import ProjectModal from './components/ProjectModal'
import AboutSection from './components/AboutSection'
import Footer from './components/Footer'
import { projects, personalInfo } from './data/projects'

function App() {
  const [selectedProject, setSelectedProject] = useState(null)
  const [currentProjectIndex, setCurrentProjectIndex] = useState(0)

  const openProjectModal = (project) => {
    setSelectedProject(project)
    setCurrentProjectIndex(projects.findIndex(p => p.id === project.id))
  }

  const closeProjectModal = () => {
    setSelectedProject(null)
  }

  const navigateProject = (direction) => {
    const newIndex = direction === 'next'
      ? (currentProjectIndex + 1) % projects.length
      : (currentProjectIndex - 1 + projects.length) % projects.length

    setCurrentProjectIndex(newIndex)
    setSelectedProject(projects[newIndex])
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header personalInfo={personalInfo} />

      <main className="pt-20">
        <ProjectGrid
          projects={projects}
          onProjectClick={openProjectModal}
          currentIndex={currentProjectIndex}
          onNavigate={navigateProject}
        />

        <AboutSection personalInfo={personalInfo} />
      </main>

      <Footer personalInfo={personalInfo} />

      {selectedProject && (
        <ProjectModal
          project={selectedProject}
          onClose={closeProjectModal}
          onNavigate={navigateProject}
          currentIndex={currentProjectIndex}
          totalProjects={projects.length}
        />
      )}
    </div>
  )
}

export default App
